# 校园二手交易平台

一个专为校园学生设计的二手物品交易平台，支持商品发布、浏览、购买和实时聊天功能。

## 功能特性

### 用户功能
- 🔐 用户注册和登录
- 👤 个人资料管理
- 📱 学生身份验证

### 商品功能
- 📦 发布二手商品
- 🔍 商品搜索和筛选
- 📷 多图片上传
- 💰 价格管理
- 📊 商品状态管理

### 交易功能
- 🛒 购物车管理
- 📋 订单管理
- 💬 买卖双方实时聊天
- ⭐ 用户评价系统

### 管理功能
- 📈 交易统计
- 🛡️ 内容审核
- 👥 用户管理

## 技术栈

### 前端
- React 18
- TypeScript
- Tailwind CSS
- Axios
- Socket.io Client

### 后端
- Node.js
- Express
- TypeScript
- SQLite
- Socket.io
- JWT认证
- Multer文件上传

## 快速开始

### 安装依赖
```bash
npm run install:all
```

### 开发模式
```bash
npm run dev
```

### 生产构建
```bash
npm run build
npm start
```

## 项目结构

```
campus-marketplace/
├── client/                 # React前端
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── pages/         # 页面
│   │   ├── hooks/         # 自定义hooks
│   │   ├── services/      # API服务
│   │   ├── types/         # TypeScript类型
│   │   └── utils/         # 工具函数
│   └── public/
├── server/                # Node.js后端
│   ├── src/
│   │   ├── controllers/   # 控制器
│   │   ├── models/        # 数据模型
│   │   ├── routes/        # 路由
│   │   ├── middleware/    # 中间件
│   │   ├── services/      # 业务服务
│   │   └── utils/         # 工具函数
│   └── uploads/           # 上传文件
└── docs/                  # 文档
```

## 开发指南

### 环境要求
- Node.js >= 16
- npm >= 8

### 开发流程
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

MIT License
