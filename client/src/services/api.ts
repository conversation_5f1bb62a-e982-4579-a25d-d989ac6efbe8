import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ApiResponse } from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || '/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token过期或无效，清除本地存储并跳转到登录页
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // 认证相关
  async login(email: string, password: string) {
    const response = await this.api.post('/auth/login', { email, password });
    return response.data;
  }

  async register(userData: any) {
    const response = await this.api.post('/auth/register', userData);
    return response.data;
  }

  async getProfile() {
    const response = await this.api.get('/auth/profile');
    return response.data;
  }

  // 商品相关
  async getProducts(params?: any) {
    const response = await this.api.get('/products', { params });
    return response.data;
  }

  async getProduct(id: string) {
    const response = await this.api.get(`/products/${id}`);
    return response.data;
  }

  async createProduct(formData: FormData) {
    const response = await this.api.post('/products', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async getMyProducts(status?: string) {
    const params = status ? { status } : {};
    const response = await this.api.get('/products/my', { params });
    return response.data;
  }

  async updateProduct(id: string, data: any) {
    const response = await this.api.put(`/products/${id}`, data);
    return response.data;
  }

  async deleteProduct(id: string) {
    const response = await this.api.delete(`/products/${id}`);
    return response.data;
  }

  // 分类相关
  async getCategories() {
    const response = await this.api.get('/categories');
    return response.data;
  }

  // 订单相关
  async createOrder(productId: string, data: any) {
    const response = await this.api.post('/orders', { productId, ...data });
    return response.data;
  }

  async getOrders(type?: 'buying' | 'selling') {
    const params = type ? { type } : {};
    const response = await this.api.get('/orders', { params });
    return response.data;
  }

  async updateOrderStatus(id: string, status: string) {
    const response = await this.api.patch(`/orders/${id}/status`, { status });
    return response.data;
  }

  // 消息相关
  async getConversations() {
    const response = await this.api.get('/messages/conversations');
    return response.data;
  }

  async getMessages(conversationId: string) {
    const response = await this.api.get(`/messages/${conversationId}`);
    return response.data;
  }

  async sendMessage(receiverId: string, content: string, type: string = 'text') {
    const response = await this.api.post('/messages', {
      receiverId,
      content,
      type,
    });
    return response.data;
  }

  // 收藏相关
  async addToFavorites(productId: string) {
    const response = await this.api.post('/favorites', { productId });
    return response.data;
  }

  async removeFromFavorites(productId: string) {
    const response = await this.api.delete(`/favorites/${productId}`);
    return response.data;
  }

  async getFavorites() {
    const response = await this.api.get('/favorites');
    return response.data;
  }

  // 文件上传
  async uploadFile(file: File, type: string = 'image') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const response = await this.api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // 获取文件URL
  getFileUrl(filename: string): string {
    return `${process.env.REACT_APP_API_URL || ''}/uploads/${filename}`;
  }
}

export const apiService = new ApiService();
export default apiService;
