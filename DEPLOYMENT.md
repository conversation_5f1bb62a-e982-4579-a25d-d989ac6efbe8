# 部署指南

## 开发环境部署

### 前置要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 快速开始

#### Windows用户
```bash
# 双击运行
start.bat
```

#### Linux/Mac用户
```bash
# 给脚本执行权限
chmod +x start.sh

# 运行脚本
./start.sh
```

#### 手动启动
```bash
# 1. 安装所有依赖
npm run install:all

# 2. 启动开发服务器
npm run dev
```

### 访问地址
- 前端: http://localhost:3000
- 后端API: http://localhost:3001/api
- 文件服务: http://localhost:3001/uploads

## 生产环境部署

### 1. 构建项目
```bash
# 构建前端
cd client
npm run build

# 构建后端
cd ../server
npm run build
```

### 2. 环境变量配置
复制 `.env.example` 为 `.env` 并修改相应配置：

```bash
# 服务器配置
PORT=3001
NODE_ENV=production

# JWT配置 - 请使用强密码
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# 数据库配置
DB_PATH=./database.sqlite

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# CORS配置
CLIENT_URL=https://your-domain.com
```

### 3. 使用PM2部署（推荐）
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start server/dist/index.js --name "campus-marketplace"

# 设置开机自启
pm2 startup
pm2 save
```

### 4. 使用Docker部署

创建 `Dockerfile`:
```dockerfile
FROM node:16-alpine

WORKDIR /app

# 复制package.json
COPY package*.json ./
COPY server/package*.json ./server/
COPY client/package*.json ./client/

# 安装依赖
RUN npm run install:all

# 复制源码
COPY . .

# 构建应用
RUN cd client && npm run build
RUN cd server && npm run build

# 暴露端口
EXPOSE 3001

# 启动应用
CMD ["npm", "start"]
```

构建和运行：
```bash
# 构建镜像
docker build -t campus-marketplace .

# 运行容器
docker run -p 3001:3001 -v $(pwd)/uploads:/app/server/uploads campus-marketplace
```

### 5. Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/client/build;
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 文件服务
    location /uploads {
        proxy_pass http://localhost:3001;
    }
}
```

## 数据库备份

### 备份SQLite数据库
```bash
# 创建备份
cp server/database.sqlite backup/database_$(date +%Y%m%d_%H%M%S).sqlite

# 定期备份（添加到crontab）
0 2 * * * cp /path/to/server/database.sqlite /path/to/backup/database_$(date +\%Y\%m\%d_\%H\%M\%S).sqlite
```

## 监控和日志

### 使用PM2监控
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs campus-marketplace

# 重启应用
pm2 restart campus-marketplace
```

### 日志轮转
```bash
# 安装pm2-logrotate
pm2 install pm2-logrotate

# 配置日志轮转
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

## 性能优化

### 1. 启用Gzip压缩
在Nginx配置中添加：
```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
```

### 2. 静态资源缓存
```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. 数据库优化
- 定期清理过期数据
- 添加适当的索引
- 考虑升级到PostgreSQL或MySQL

## 安全建议

1. **使用HTTPS**: 配置SSL证书
2. **防火墙**: 只开放必要端口
3. **定期更新**: 保持依赖包最新
4. **备份策略**: 定期备份数据库和文件
5. **监控**: 设置应用和服务器监控
6. **日志审计**: 定期检查访问日志

## 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查找占用端口的进程
netstat -tulpn | grep :3001
# 或
lsof -i :3001

# 杀死进程
kill -9 <PID>
```

2. **权限问题**
```bash
# 确保上传目录有写权限
chmod 755 server/uploads
```

3. **内存不足**
```bash
# 增加Node.js内存限制
node --max-old-space-size=4096 server/dist/index.js
```

4. **数据库锁定**
```bash
# 重启应用释放数据库连接
pm2 restart campus-marketplace
```
