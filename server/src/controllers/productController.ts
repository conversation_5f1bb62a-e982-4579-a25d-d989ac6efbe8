import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import Jo<PERSON> from 'joi';
import { db } from '../config/database';
import { AuthRequest, Product } from '../types';

// 验证schemas
const createProductSchema = Joi.object({
  title: Joi.string().min(1).max(100).required().messages({
    'string.min': '商品标题不能为空',
    'string.max': '商品标题最多100个字符',
    'any.required': '商品标题是必填项'
  }),
  description: Joi.string().min(10).max(1000).required().messages({
    'string.min': '商品描述至少10个字符',
    'string.max': '商品描述最多1000个字符',
    'any.required': '商品描述是必填项'
  }),
  price: Joi.number().min(0).required().messages({
    'number.min': '价格不能为负数',
    'any.required': '价格是必填项'
  }),
  category: Joi.string().required().messages({
    'any.required': '商品分类是必填项'
  }),
  condition: Joi.string().valid('new', 'like-new', 'good', 'fair', 'poor').required(),
  location: Joi.string().required().messages({
    'any.required': '交易地点是必填项'
  }),
  tags: Joi.array().items(Joi.string()).optional()
});

export const createProduct = async (req: AuthRequest, res: Response) => {
  try {
    const { error, value } = createProductSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { title, description, price, category, condition, location, tags } = value;
    const sellerId = req.user!.id;

    // 处理上传的图片
    const images = req.files ? (req.files as Express.Multer.File[]).map(file => file.filename) : [];

    if (images.length === 0) {
      return res.status(400).json({
        success: false,
        message: '至少需要上传一张商品图片'
      });
    }

    // 创建商品
    const productId = uuidv4();
    await db.run(
      `INSERT INTO products (id, title, description, price, category, condition, images, sellerId, location, tags, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))`,
      [
        productId,
        title,
        description,
        price,
        category,
        condition,
        JSON.stringify(images),
        sellerId,
        location,
        JSON.stringify(tags || [])
      ]
    );

    // 获取创建的商品信息
    const product = await db.get('SELECT * FROM products WHERE id = ?', [productId]);
    
    res.status(201).json({
      success: true,
      message: '商品发布成功',
      data: {
        ...product,
        images: JSON.parse(product.images),
        tags: JSON.parse(product.tags)
      }
    });
  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

export const getProducts = async (req: Request, res: Response) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      condition,
      minPrice,
      maxPrice,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = req.query;

    const offset = (Number(page) - 1) * Number(limit);
    
    // 构建查询条件
    let whereClause = "WHERE status = 'available'";
    const params: any[] = [];

    if (category) {
      whereClause += ' AND category = ?';
      params.push(category);
    }

    if (condition) {
      whereClause += ' AND condition = ?';
      params.push(condition);
    }

    if (minPrice) {
      whereClause += ' AND price >= ?';
      params.push(Number(minPrice));
    }

    if (maxPrice) {
      whereClause += ' AND price <= ?';
      params.push(Number(maxPrice));
    }

    if (search) {
      whereClause += ' AND (title LIKE ? OR description LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    // 获取商品列表
    const products = await db.all(
      `SELECT p.*, u.username as sellerName, u.rating as sellerRating
       FROM products p
       JOIN users u ON p.sellerId = u.id
       ${whereClause}
       ORDER BY p.${sortBy} ${sortOrder}
       LIMIT ? OFFSET ?`,
      [...params, Number(limit), offset]
    );

    // 获取总数
    const totalResult = await db.get(
      `SELECT COUNT(*) as total FROM products p ${whereClause}`,
      params
    );

    const formattedProducts = products.map(product => ({
      ...product,
      images: JSON.parse(product.images || '[]'),
      tags: JSON.parse(product.tags || '[]')
    }));

    res.json({
      success: true,
      message: '获取商品列表成功',
      data: {
        products: formattedProducts,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: totalResult.total,
          totalPages: Math.ceil(totalResult.total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

export const getProduct = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    // 增加浏览次数
    await db.run('UPDATE products SET viewCount = viewCount + 1 WHERE id = ?', [id]);

    // 获取商品详情
    const product = await db.get(
      `SELECT p.*, u.username as sellerName, u.rating as sellerRating, u.avatar as sellerAvatar
       FROM products p
       JOIN users u ON p.sellerId = u.id
       WHERE p.id = ?`,
      [id]
    );

    if (!product) {
      return res.status(404).json({
        success: false,
        message: '商品不存在'
      });
    }

    res.json({
      success: true,
      message: '获取商品详情成功',
      data: {
        ...product,
        images: JSON.parse(product.images || '[]'),
        tags: JSON.parse(product.tags || '[]')
      }
    });
  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

export const getMyProducts = async (req: AuthRequest, res: Response) => {
  try {
    const sellerId = req.user!.id;
    const { status } = req.query;

    let whereClause = 'WHERE sellerId = ?';
    const params = [sellerId];

    if (status) {
      whereClause += ' AND status = ?';
      params.push(status as string);
    }

    const products = await db.all(
      `SELECT * FROM products ${whereClause} ORDER BY createdAt DESC`,
      params
    );

    const formattedProducts = products.map(product => ({
      ...product,
      images: JSON.parse(product.images || '[]'),
      tags: JSON.parse(product.tags || '[]')
    }));

    res.json({
      success: true,
      message: '获取我的商品成功',
      data: formattedProducts
    });
  } catch (error) {
    console.error('Get my products error:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
