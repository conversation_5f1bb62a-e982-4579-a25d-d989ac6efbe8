import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import Jo<PERSON> from 'joi';
import { db } from '../config/database';
import { hashPassword, comparePassword, generateToken } from '../utils/auth';
import { User, ApiResponse } from '../types';

// 验证schemas
const registerSchema = Joi.object({
  username: Joi.string().min(3).max(30).required().messages({
    'string.min': '用户名至少3个字符',
    'string.max': '用户名最多30个字符',
    'any.required': '用户名是必填项'
  }),
  email: Joi.string().email().required().messages({
    'string.email': '请输入有效的邮箱地址',
    'any.required': '邮箱是必填项'
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': '密码至少6个字符',
    'any.required': '密码是必填项'
  }),
  studentId: Joi.string().required().messages({
    'any.required': '学号是必填项'
  }),
  school: Joi.string().required().messages({
    'any.required': '学校是必填项'
  }),
  phone: Joi.string().optional()
});

const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required()
});

export const register = async (req: Request, res: Response) => {
  try {
    const { error, value } = registerSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { username, email, password, studentId, school, phone } = value;

    // 检查用户是否已存在
    const existingUser = await db.get(
      'SELECT id FROM users WHERE email = ? OR username = ? OR studentId = ?',
      [email, username, studentId]
    );

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名、邮箱或学号已被使用'
      });
    }

    // 创建新用户
    const userId = uuidv4();
    const hashedPassword = await hashPassword(password);

    await db.run(
      `INSERT INTO users (id, username, email, password, studentId, school, phone, createdAt, updatedAt)
       VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))`,
      [userId, username, email, hashedPassword, studentId, school, phone]
    );

    // 获取创建的用户信息
    const newUser = await db.get('SELECT * FROM users WHERE id = ?', [userId]);
    const { password: _, ...userWithoutPassword } = newUser;

    // 生成token
    const token = generateToken(newUser);

    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        user: userWithoutPassword,
        token
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

export const login = async (req: Request, res: Response) => {
  try {
    const { error, value } = loginSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const { email, password } = value;

    // 查找用户
    const user = await db.get('SELECT * FROM users WHERE email = ?', [email]);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      });
    }

    // 验证密码
    const isValidPassword = await comparePassword(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      });
    }

    // 生成token
    const token = generateToken(user);
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: userWithoutPassword,
        token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

export const getProfile = async (req: Request, res: Response) => {
  try {
    const user = (req as any).user;
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      message: '获取用户信息成功',
      data: userWithoutPassword
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
