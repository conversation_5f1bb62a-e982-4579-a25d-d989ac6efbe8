import { Router } from 'express';
import { createProduct, getProducts, getProduct, getMyProducts } from '../controllers/productController';
import { authenticateToken, optionalAuth } from '../middleware/auth';
import { uploadMultiple } from '../middleware/upload';

const router = Router();

// 创建商品
router.post('/', authenticateToken, uploadMultiple, createProduct);

// 获取商品列表
router.get('/', optionalAuth, getProducts);

// 获取我的商品
router.get('/my', authenticateToken, getMyProducts);

// 获取商品详情
router.get('/:id', optionalAuth, getProduct);

export default router;
