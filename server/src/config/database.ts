import sqlite3 from 'sqlite3';
import path from 'path';
import { promisify } from 'util';

const dbPath = process.env.DB_PATH || path.join(__dirname, '../../database.sqlite');

class Database {
  private db: sqlite3.Database;

  constructor() {
    this.db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err);
      } else {
        console.log('Connected to SQLite database');
        this.initTables();
      }
    });
  }

  private async initTables() {
    const tables = [
      `CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        studentId TEXT UNIQUE NOT NULL,
        school TEXT NOT NULL,
        phone TEXT,
        avatar TEXT,
        rating REAL DEFAULT 5.0,
        isVerified INTEGER DEFAULT 0,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
      )`,
      
      `CREATE TABLE IF NOT EXISTS categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        icon TEXT,
        parentId TEXT,
        FOREIGN KEY (parentId) REFERENCES categories (id)
      )`,
      
      `CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        price REAL NOT NULL,
        category TEXT NOT NULL,
        condition TEXT NOT NULL,
        status TEXT DEFAULT 'available',
        images TEXT,
        sellerId TEXT NOT NULL,
        location TEXT NOT NULL,
        tags TEXT,
        viewCount INTEGER DEFAULT 0,
        favoriteCount INTEGER DEFAULT 0,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        updatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (sellerId) REFERENCES users (id),
        FOREIGN KEY (category) REFERENCES categories (id)
      )`,
      
      `CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        productId TEXT NOT NULL,
        buyerId TEXT NOT NULL,
        sellerId TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        totalAmount REAL NOT NULL,
        meetingLocation TEXT,
        meetingTime TEXT,
        notes TEXT,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        updatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (productId) REFERENCES products (id),
        FOREIGN KEY (buyerId) REFERENCES users (id),
        FOREIGN KEY (sellerId) REFERENCES users (id)
      )`,
      
      `CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        senderId TEXT NOT NULL,
        receiverId TEXT NOT NULL,
        content TEXT NOT NULL,
        type TEXT DEFAULT 'text',
        isRead INTEGER DEFAULT 0,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (senderId) REFERENCES users (id),
        FOREIGN KEY (receiverId) REFERENCES users (id)
      )`,
      
      `CREATE TABLE IF NOT EXISTS reviews (
        id TEXT PRIMARY KEY,
        orderId TEXT NOT NULL,
        reviewerId TEXT NOT NULL,
        revieweeId TEXT NOT NULL,
        rating INTEGER NOT NULL,
        comment TEXT,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (orderId) REFERENCES orders (id),
        FOREIGN KEY (reviewerId) REFERENCES users (id),
        FOREIGN KEY (revieweeId) REFERENCES users (id)
      )`,
      
      `CREATE TABLE IF NOT EXISTS favorites (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        productId TEXT NOT NULL,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (userId) REFERENCES users (id),
        FOREIGN KEY (productId) REFERENCES products (id),
        UNIQUE(userId, productId)
      )`
    ];

    for (const table of tables) {
      await this.run(table);
    }

    // 插入默认分类
    await this.insertDefaultCategories();
  }

  private async insertDefaultCategories() {
    const categories = [
      { id: 'electronics', name: '电子产品', description: '手机、电脑、数码产品等', icon: '📱' },
      { id: 'books', name: '图书教材', description: '教科书、参考书、小说等', icon: '📚' },
      { id: 'clothing', name: '服装配饰', description: '衣服、鞋子、包包等', icon: '👕' },
      { id: 'sports', name: '运动用品', description: '运动器材、健身用品等', icon: '⚽' },
      { id: 'furniture', name: '家具用品', description: '桌椅、床铺、生活用品等', icon: '🪑' },
      { id: 'others', name: '其他物品', description: '其他各类物品', icon: '📦' }
    ];

    for (const category of categories) {
      await this.run(
        'INSERT OR IGNORE INTO categories (id, name, description, icon) VALUES (?, ?, ?, ?)',
        [category.id, category.name, category.description, category.icon]
      );
    }
  }

  public run(sql: string, params: any[] = []): Promise<sqlite3.RunResult> {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) reject(err);
        else resolve(this);
      });
    });
  }

  public get(sql: string, params: any[] = []): Promise<any> {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  public all(sql: string, params: any[] = []): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  public close(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }
}

export const db = new Database();
