import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import path from 'path';
import { createServer } from 'http';
import { Server } from 'socket.io';

// 加载环境变量
dotenv.config();

import routes from './routes';
import { db } from './config/database';

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.CLIENT_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// 中间件
app.use(helmet());
app.use(cors({
  origin: process.env.CLIENT_URL || "http://localhost:3000",
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// API路由
app.use('/api', routes);

// Socket.io 连接处理
io.on('connection', (socket) => {
  console.log('用户连接:', socket.id);

  // 加入房间（用于私聊）
  socket.on('join-room', (roomId) => {
    socket.join(roomId);
    console.log(`用户 ${socket.id} 加入房间 ${roomId}`);
  });

  // 发送消息
  socket.on('send-message', async (data) => {
    try {
      const { senderId, receiverId, content, type = 'text' } = data;
      
      // 保存消息到数据库
      const messageId = require('uuid').v4();
      await db.run(
        'INSERT INTO messages (id, senderId, receiverId, content, type, createdAt) VALUES (?, ?, ?, ?, ?, datetime("now"))',
        [messageId, senderId, receiverId, content, type]
      );

      // 创建房间ID（确保一致性）
      const roomId = [senderId, receiverId].sort().join('-');
      
      // 发送消息到房间
      io.to(roomId).emit('new-message', {
        id: messageId,
        senderId,
        receiverId,
        content,
        type,
        isRead: false,
        createdAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('发送消息错误:', error);
      socket.emit('error', { message: '发送消息失败' });
    }
  });

  // 标记消息为已读
  socket.on('mark-read', async (data) => {
    try {
      const { messageIds } = data;
      await db.run(
        `UPDATE messages SET isRead = 1 WHERE id IN (${messageIds.map(() => '?').join(',')})`,
        messageIds
      );
    } catch (error) {
      console.error('标记已读错误:', error);
    }
  });

  socket.on('disconnect', () => {
    console.log('用户断开连接:', socket.id);
  });
});

// 错误处理中间件
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`🚀 服务器运行在端口 ${PORT}`);
  console.log(`📱 API地址: http://localhost:${PORT}/api`);
  console.log(`📁 文件服务: http://localhost:${PORT}/uploads`);
});

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  await db.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  await db.close();
  process.exit(0);
});

export default app;
